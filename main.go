package main

import (
	"bytes"
	"fmt"
	"log"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/gnet/v2"
)

const (
	// Connection timeout duration (30 seconds)
	connectionTimeout = 30 * time.Second
	// Cleanup interval for checking timeouts (every 5 seconds)
	cleanupInterval = 5 * time.Second
	// UI update interval (every 1 second)
	uiUpdateInterval = 1 * time.Second
)

var (
	blankResponse   = []byte("HTTP/1.1 200 OK\r\nContent-Length: 0\r\n\r\n")
	pathNginxStatus = []byte("/nginx_status")
	pathStatus      = []byte("/status")
	doubleCRLF      = []byte("\r\n\r\n")
	hostHeader      = []byte("host:")

	// Pre-defined subdomain patterns for zero-copy matching
	jsSubdomain      = []byte("js.relayed.network")
	uamSubdomain     = []byte("uam.relayed.network")
	managedSubdomain = []byte("managed.relayed.network")
)

var (
	statusHeaderPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 128)
		},
	}
	statusBodyPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 256)
		},
	}
)

// ConnectionInfo holds connection metadata for timeout tracking
type ConnectionInfo struct {
	lastActivity time.Time
	conn         gnet.Conn
}

// SubdomainStats holds statistics for a specific subdomain
type SubdomainStats struct {
	connections int64
	accepts     int64
	handled     int64
	requests    int64
	reading     int64
	// RPS tracking
	lastRequests int64
	currentRPS   int64
}

// Subdomain indices for array-based lookup (faster than map)
const (
	subdomainJS = iota
	subdomainUAM
	subdomainManaged
	subdomainDefault
	subdomainCount
)

// Subdomain names for display
var subdomainNames = [subdomainCount]string{
	subdomainJS:      "js.relayed.network",
	subdomainUAM:     "uam.relayed.network",
	subdomainManaged: "managed.relayed.network",
	subdomainDefault: "default/other",
}

type rpsServer struct {
	gnet.BuiltinEventEngine
	addr      string
	multicore bool
	eng       gnet.Engine
	// Global stats (for backward compatibility)
	connections int64
	requests    int64
	reading     int64
	// Pre-allocated per-subdomain stats (no map lookups, no mutex needed)
	subdomainStats [subdomainCount]SubdomainStats
	// Connection tracking for timeouts
	connTracker sync.Map // map[int]*ConnectionInfo - using sync.Map for concurrent access
	// UI state
	startTime    time.Time
	uiTicker     *time.Ticker
	lastUIUpdate time.Time
}

// extractSubdomainIndex extracts subdomain index from host header value using zero-copy byte operations
func extractSubdomainIndex(hostValue []byte) int {
	// Remove port if present (find colon)
	colonIndex := bytes.IndexByte(hostValue, ':')
	if colonIndex >= 0 {
		hostValue = hostValue[:colonIndex]
	}

	// Fast byte-based matching for known subdomains
	if bytes.Equal(hostValue, jsSubdomain) {
		return subdomainJS
	}
	if bytes.Equal(hostValue, uamSubdomain) {
		return subdomainUAM
	}
	if bytes.Equal(hostValue, managedSubdomain) {
		return subdomainManaged
	}

	// Default for unknown hosts
	return subdomainDefault
}

func (rs *rpsServer) OnBoot(eng gnet.Engine) (action gnet.Action) {
	rs.eng = eng
	rs.startTime = time.Now()
	rs.lastUIUpdate = time.Now()

	// Start UI update goroutine
	go rs.runUI()

	// No need to initialize subdomainStats array - zero values are fine
	return
}

func (rs *rpsServer) OnOpen(c gnet.Conn) (out []byte, action gnet.Action) {
	atomic.AddInt64(&rs.connections, 1)

	// Track connection for timeout management
	now := time.Now()
	connInfo := &ConnectionInfo{
		lastActivity: now,
		conn:         c,
	}
	rs.connTracker.Store(c.Fd(), connInfo)

	return
}

func (rs *rpsServer) OnClose(c gnet.Conn, err error) (action gnet.Action) {
	atomic.AddInt64(&rs.connections, -1)

	// Remove connection from tracker
	rs.connTracker.Delete(c.Fd())

	return
}

func (rs *rpsServer) OnTraffic(c gnet.Conn) (action gnet.Action) {
	atomic.AddInt64(&rs.requests, 1)
	atomic.AddInt64(&rs.reading, 1)
	defer atomic.AddInt64(&rs.reading, -1)

	// Update last activity time for timeout tracking
	if connInfo, ok := rs.connTracker.Load(c.Fd()); ok {
		connInfo.(*ConnectionInfo).lastActivity = time.Now()
	}

	// Peek(-1) returns all bytes in the ring-buffer without copying. This is a zero-copy operation.
	buf, err := c.Peek(-1)
	if err != nil {
		return gnet.Close
	}

	// Find the end of the HTTP request, marked by a double CRLF.
	idx := bytes.Index(buf, doubleCRLF)
	if idx < 0 {
		// Incomplete request, wait for more data.
		return gnet.None
	}
	requestLen := idx + len(doubleCRLF)

	// Defer the discard operation. It will be executed on all return paths from this point forward,
	// ensuring the processed data is removed from the buffer before the connection is closed.
	defer func() {
		_, _ = c.Discard(requestLen)
	}()

	// Create a slice that views the request data from the buffer. This is also a zero-copy operation.
	data := buf[:requestLen]

	// Parse HTTP request to extract path and host
	lineEnd := bytes.IndexByte(data, '\n')
	if lineEnd < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	requestLine := data[:lineEnd]
	if len(requestLine) > 0 && requestLine[len(requestLine)-1] == '\r' {
		requestLine = requestLine[:len(requestLine)-1]
	}

	var first, second int = -1, -1
	for i := 0; i < len(requestLine); i++ {
		if requestLine[i] == ' ' {
			if first < 0 {
				first = i
			} else {
				second = i
				break
			}
		}
	}
	if first < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	if second < 0 {
		second = len(requestLine)
	}
	path := requestLine[first+1 : second]

	// Extract Host header using zero-copy byte operations
	subdomainIndex := subdomainDefault
	headerStart := lineEnd + 1
	for headerStart < len(data) {
		headerEnd := bytes.IndexByte(data[headerStart:], '\n')
		if headerEnd < 0 {
			break
		}
		headerEnd += headerStart

		headerLine := data[headerStart:headerEnd]
		if len(headerLine) > 0 && headerLine[len(headerLine)-1] == '\r' {
			headerLine = headerLine[:len(headerLine)-1]
		}

		// Fast case-insensitive comparison for "host:" header
		if len(headerLine) > 5 &&
			(headerLine[0] == 'h' || headerLine[0] == 'H') &&
			(headerLine[1] == 'o' || headerLine[1] == 'O') &&
			(headerLine[2] == 's' || headerLine[2] == 'S') &&
			(headerLine[3] == 't' || headerLine[3] == 'T') &&
			headerLine[4] == ':' {

			// Extract host value (skip "host:" and trim spaces)
			hostStart := 5
			for hostStart < len(headerLine) && (headerLine[hostStart] == ' ' || headerLine[hostStart] == '\t') {
				hostStart++
			}
			if hostStart < len(headerLine) {
				hostValue := headerLine[hostStart:]
				subdomainIndex = extractSubdomainIndex(hostValue)
			}
			break
		}

		headerStart = headerEnd + 1
	}

	// Update per-subdomain statistics using direct array access (no locks, no allocations)
	subStats := &rs.subdomainStats[subdomainIndex]
	atomic.AddInt64(&subStats.accepts, 1)
	atomic.AddInt64(&subStats.handled, 1)
	atomic.AddInt64(&subStats.requests, 1)
	atomic.AddInt64(&subStats.reading, 1)
	defer atomic.AddInt64(&subStats.reading, -1)

	if bytes.Equal(path, pathNginxStatus) || bytes.Equal(path, pathStatus) {
		// Use subdomain-specific statistics
		subdomainConnections := atomic.LoadInt64(&rs.connections) // Global connections for now
		subdomainAccepts := atomic.LoadInt64(&subStats.accepts)
		subdomainHandled := atomic.LoadInt64(&subStats.handled)
		subdomainRequests := atomic.LoadInt64(&subStats.requests)
		subdomainReading := atomic.LoadInt64(&subStats.reading)
		subdomainWaiting := subdomainConnections - subdomainReading
		if subdomainWaiting < 0 {
			subdomainWaiting = 0
		}

		// build body in exact nginx status format
		body := statusBodyPool.Get().([]byte)
		body = body[:0]
		body = append(body, "Active connections: "...)
		body = strconv.AppendInt(body, subdomainConnections, 10)
		body = append(body, " \nserver accepts handled requests\n "...)
		body = strconv.AppendInt(body, subdomainAccepts, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainHandled, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainRequests, 10)
		body = append(body, " \nReading: "...)
		body = strconv.AppendInt(body, subdomainReading, 10)
		body = append(body, " Writing: "...)
		body = strconv.AppendInt(body, 0, 10)
		body = append(body, " Waiting: "...)
		body = strconv.AppendInt(body, subdomainWaiting, 10)
		body = append(body, " \n"...)

		// build header
		header := statusHeaderPool.Get().([]byte)
		header = header[:0]
		header = append(header, "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: "...)
		header = strconv.AppendInt(header, int64(len(body)), 10)
		header = append(header, "\r\n\r\n"...)

		c.Writev([][]byte{header, body})
		statusHeaderPool.Put(header)
		statusBodyPool.Put(body)
	} else {
		c.Write(blankResponse)
	}

	return gnet.Close
}

// runUI runs the text-based UI in a separate goroutine
func (rs *rpsServer) runUI() {
	ticker := time.NewTicker(uiUpdateInterval)
	defer ticker.Stop()

	// Clear screen and hide cursor
	fmt.Print("\033[2J\033[?25l")

	for range ticker.C {
		rs.updateUI()
	}
}

// updateUI refreshes the console display
func (rs *rpsServer) updateUI() {
	now := time.Now()
	uptime := now.Sub(rs.startTime)

	// Calculate RPS for each subdomain
	for i := 0; i < subdomainCount; i++ {
		currentRequests := atomic.LoadInt64(&rs.subdomainStats[i].requests)
		lastRequests := atomic.LoadInt64(&rs.subdomainStats[i].lastRequests)
		rps := currentRequests - lastRequests
		atomic.StoreInt64(&rs.subdomainStats[i].currentRPS, rps)
		atomic.StoreInt64(&rs.subdomainStats[i].lastRequests, currentRequests)
	}

	// Move cursor to top-left and clear screen
	fmt.Print("\033[H\033[2J")

	// Header
	fmt.Printf("╔══════════════════════════════════════════════════════════════════════════════╗\n")
	fmt.Printf("║                            RPS Server Monitor                               ║\n")
	fmt.Printf("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	fmt.Printf("║ Uptime: %-20s │ Active Connections: %-10d │ Port: 9000    ║\n",
		rs.formatDuration(uptime), atomic.LoadInt64(&rs.connections))
	fmt.Printf("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	fmt.Printf("║                              Subdomain Statistics                           ║\n")
	fmt.Printf("╠═══════════════════════════════╤═══════╤═══════╤═══════╤═══════╤═══════════╣\n")
	fmt.Printf("║ Subdomain                     │  RPS  │ Total │ Activ │ Read  │ Handled   ║\n")
	fmt.Printf("╠═══════════════════════════════╪═══════╪═══════╪═══════╪═══════╪═══════════╣\n")

	// Subdomain stats
	for i := 0; i < subdomainCount; i++ {
		stats := &rs.subdomainStats[i]
		name := subdomainNames[i]
		if len(name) > 29 {
			name = name[:26] + "..."
		}

		rps := atomic.LoadInt64(&stats.currentRPS)
		total := atomic.LoadInt64(&stats.requests)
		connections := atomic.LoadInt64(&stats.connections)
		reading := atomic.LoadInt64(&stats.reading)
		handled := atomic.LoadInt64(&stats.handled)

		fmt.Printf("║ %-29s │ %5d │ %5d │ %5d │ %5d │ %9d ║\n",
			name, rps, total, connections, reading, handled)
	}

	fmt.Printf("╚═══════════════════════════════╧═══════╧═══════╧═══════╧═══════╧═══════════╝\n")
	fmt.Printf("\nPress Ctrl+C to stop the server\n")
	fmt.Printf("Last updated: %s\n", now.Format("15:04:05"))
}

// formatDuration formats a duration in a human-readable way
func (rs *rpsServer) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0fm %.0fs", d.Minutes(), d.Seconds()-60*d.Minutes())
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) - hours*60
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
}

// OnTick implements periodic timeout checking for connections
func (rs *rpsServer) OnTick() (delay time.Duration, action gnet.Action) {
	now := time.Now()
	var connectionsToClose []gnet.Conn

	// Iterate through all tracked connections to find timed-out ones
	rs.connTracker.Range(func(key, value interface{}) bool {
		connInfo := value.(*ConnectionInfo)
		if now.Sub(connInfo.lastActivity) > connectionTimeout {
			// Collect connections to close outside the Range loop
			// to avoid modifying the map while iterating
			connectionsToClose = append(connectionsToClose, connInfo.conn)
		}
		return true // continue iteration
	})

	// Close timed-out connections
	if len(connectionsToClose) > 0 {
		log.Printf("Closing %d timed-out connections (idle > %v)", len(connectionsToClose), connectionTimeout)
	}
	for _, conn := range connectionsToClose {
		conn.Close() // This will trigger OnClose which removes from tracker
	}

	// Return cleanup interval for next tick
	return cleanupInterval, gnet.None
}

func main() {
	rs := &rpsServer{
		addr:      "tcp://:9000",
		multicore: true,
	}

	// Setup graceful shutdown to restore cursor
	defer func() {
		fmt.Print("\033[?25h") // Show cursor
		if r := recover(); r != nil {
			log.Printf("Server panic: %v", r)
		}
	}()

	log.Printf("Starting RPS server on %s", rs.addr)
	log.Printf("Connection timeout: %v", connectionTimeout)
	log.Printf("Cleanup interval: %v", cleanupInterval)
	log.Printf("Multicore mode: %v", rs.multicore)
	log.Printf("UI will start after server boot...")

	log.Fatal(gnet.Run(rs, rs.addr, gnet.WithMulticore(rs.multicore)))
}
